import { Component, inject, OnInit, signal } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Effects } from './store/effects';
import { Auth } from '@angular/fire/auth';
import { selectedTheme$ } from './store/signals';
import { Functions, httpsCallable } from '@angular/fire/functions';

@Component({
  imports: [RouterModule],
  selector: 'app-root',
  templateUrl: './app.html',
  styleUrl: './app.css',
  standalone: true,
})
export class App implements OnInit {
  private effects: Effects = inject(Effects);
  private functions: Functions = inject(Functions);
  protected auth: Auth = inject(Auth);
  protected readonly selectedTheme$ = selectedTheme$;
  protected appReady = signal(false);

  ngOnInit() {
    this.callHelloWorld().catch(console.error);
    setTimeout(() => {
      this.appReady.set(true);
      this.logUserDetails().catch(console.error);
    }, 1000);
  }

  async logUserDetails() {
    try {
      const user = this.auth.currentUser;
      if (user) {
        const tokenResult = await user.getIdTokenResult();
        console.log('Claims', tokenResult.claims, tokenResult.claims?.['role']);
      } else {
        console.warn('No user logged in');
      }
    } catch (e) {
      console.error(e);
    }
  }

  async callHelloWorld() {
    const helloWorld = httpsCallable(this.functions, 'helloWorld');
    try {
      const result = await helloWorld({});
      console.log(result.data);
    } catch (error) {
      console.error(error);
    }
  }
}
