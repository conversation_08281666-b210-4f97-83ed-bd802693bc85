import { Component, Input } from '@angular/core';
import { selectedTheme$ } from '../../store/signals';
import { NgClass } from '@angular/common';

@Component({
  selector: 'app-theme-switcher',
  imports: [NgClass],
  templateUrl: './theme-switcher.html',
})
export class ThemeSwitcher {
  @Input() dropdown = true;

  DAISY_THEMES = [
    'light',
    'dark',
    'cupcake',
    'bumblebee',
    'emerald',
    'corporate',
    'synthwave',
    'retro',
    'cyberpunk',
    'valentine',
    'halloween',
    'garden',
    'forest',
    'aqua',
    'lofi',
    'pastel',
    'fantasy',
    'wireframe',
    'black',
    'luxury',
    'dracula',
    'cmyk',
    'autumn',
    'business',
    'acid',
    'lemonade',
    'night',
    'coffee',
    'winter',
    'dim',
    'nord',
    'sunset',
    'caramellatte',
    'abyss',
    'silk',
  ];
  protected readonly selectedTheme$ = selectedTheme$;

  updateTheme(theme: string) {
    console.log('Updating theme to: ', theme, '');
    localStorage.setItem('theme', theme);
    selectedTheme$.set(theme);
  }
}
