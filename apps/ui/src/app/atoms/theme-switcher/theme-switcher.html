@if (dropdown) {
  <div class="dropdown w-full">
    <div class="btn btn-ghost m-1 btn-block" role="button" tabindex="0">
      <div class="bg-base-100 grid shrink-0 grid-cols-2 gap-0.5 rounded-md p-1 shadow-sm">
        <div class="bg-base-content size-1 rounded-full"></div>
        <div class="bg-primary size-1 rounded-full"></div>
        <div class="bg-secondary size-1 rounded-full"></div>
        <div class="bg-accent size-1 rounded-full"></div>
      </div>
      <span>{{ selectedTheme$() }}</span>
      <svg class="mt-px hidden size-2 fill-current opacity-60 sm:inline-block" height="12px" viewBox="0 0 2048 2048"
           width="12px"
           xmlns="http://www.w3.org/2000/svg">
        <path d="M1799 349l242 241-1017 1017L7 590l242-241 775 775 775-775z"></path>
      </svg>
    </div>
    <ul
      class="dropdown-content bg-base-300 text-base-content rounded-box z-1 w-52 p-2 shadow-2xl max-h-96 overflow-y-auto">
      @for (theme of DAISY_THEMES; track theme) {
        <li>
          <input
            type="radio"
            name="theme-dropdown"
            [checked]="selectedTheme$() === theme"
            (change)="updateTheme(theme)"
            class="theme-controller btn btn-sm btn-block justify-start"
            [attr.aria-label]="theme.toUpperCase()"
            [value]="theme" />
        </li>
      }
    </ul>
  </div>
} @else {
  <div class="flex gap-5 flex-wrap">
    @for (theme of DAISY_THEMES; track theme) {
      <div (click)="updateTheme(theme)" role="button" tabindex="0"
           class="overflow-hidden rounded-lg outline-2 outline-offset-2"
           [ngClass]="{ 'border border-dashed': selectedTheme$() === theme }">
        <input type="radio"
               name="theme-dropdown"
               class="theme-controller hidden"
               [value]="theme"
               [checked]="selectedTheme$() === theme"
               [attr.aria-label]="theme.toUpperCase()"
               (change)="updateTheme(theme)" />
        <div class="bg-base-100 text-base-content w-full cursor-pointer font-sans" [attr.data-theme]="theme">
          <div class="grid grid-cols-5 grid-rows-3">
            <div class="bg-base-200 col-start-1 row-span-2 row-start-1"></div>
            <div class="bg-base-300 col-start-1 row-start-3"></div>
            <div class="bg-base-100 col-span-4 col-start-2 row-span-3 row-start-1 flex flex-col gap-1 p-2">
              <div class="font-bold">{{ theme }}</div>
              <div class="flex flex-wrap gap-1">
                <div class="bg-primary flex aspect-square w-5 items-center justify-center rounded lg:w-6">
                  <div class="text-primary-content text-sm font-bold">A</div>
                </div>
                <div class="bg-secondary flex aspect-square w-5 items-center justify-center rounded lg:w-6">
                  <div class="text-secondary-content text-sm font-bold">A</div>
                </div>
                <div class="bg-accent flex aspect-square w-5 items-center justify-center rounded lg:w-6">
                  <div class="text-accent-content text-sm font-bold">A</div>
                </div>
                <div class="bg-neutral flex aspect-square w-5 items-center justify-center rounded lg:w-6">
                  <div class="text-neutral-content text-sm font-bold">A</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    }
  </div>
}
