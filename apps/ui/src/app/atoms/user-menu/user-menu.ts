import { Component, inject } from '@angular/core';
import { Auth } from '@angular/fire/auth';
import { Router, RouterLink } from '@angular/router';

@Component({
  selector: 'app-user-menu',
  imports: [RouterLink],
  templateUrl: './user-menu.html',
})
export class UserMenu {
  protected auth: Auth = inject(Auth);
  private router: Router = inject(Router);

  async logout() {
    await this.auth.signOut();
    await this.router.navigate(['/']);
  }
}
