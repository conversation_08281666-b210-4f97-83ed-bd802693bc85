@if (auth?.currentUser?.displayName) {
  <div class="dropdown dropdown-end">
    <div tabindex="0" role="button" class="btn btn-ghost flex gap-3 items-center">
      <span class="icon-[qlementine-icons--user-24]" style="width: 28px; height: 28px;"></span>
      <span class="text-lg">{{ auth.currentUser.displayName }}</span>
    </div>
    <ul
      tabindex="0"
      class="menu menu-sm dropdown-content bg-primary/90 rounded-box z-1 mt-3 w-52 p-2 shadow">
      <li>
        <button>
          <span class="icon-[mingcute--profile-line]"></span>
          Profile
        </button>
      </li>
      <li>
        <button routerLink="/system/settings">
          <span class="icon-[iconamoon--settings-thin]"></span>
          Settings
        </button>
      </li>
      <li>
        <button (click)="logout()">
          <span class="icon-[streamline-sharp--logout-2]"></span>
          Logout
        </button>
      </li>
    </ul>
  </div>
} @else {
  <a class="btn btn-primary" routerLink="/auth/login">Login</a>
}
