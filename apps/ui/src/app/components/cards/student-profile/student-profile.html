<div class="card bg-base-100 shadow-sm shadow-primary">
  <div class="card-body">
    <h2 class="card-title">Student Registration Form</h2>
    <form (ngSubmit)="registerStudent()" [formGroup]="registrationForm">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Student ID*</span>
          </legend>
          <input class="input input-bordered" formControlName="studentId" placeholder="Student ID" type="text" />
          @if (registrationForm.get('studentId')?.invalid && registrationForm.get('studentId')?.touched) {
            <div class="text-error text-sm mt-1">Student ID is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">First Name*</span>
          </legend>
          <input class="input input-bordered" formControlName="firstName" placeholder="First Name" type="text" />
          @if (registrationForm.get('firstName')?.invalid && registrationForm.get('firstName')?.touched) {
            <div class="text-error text-sm mt-1">First Name is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Last Name*</span>
          </legend>
          <input class="input input-bordered" formControlName="lastName" placeholder="Last Name" type="text" />
          @if (registrationForm.get('lastName')?.invalid && registrationForm.get('lastName')?.touched) {
            <div class="text-error text-sm mt-1">Last Name is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Email Address*</span>
          </legend>
          <input class="input input-bordered" formControlName="email" placeholder="Email Address" type="email" />
          @if (registrationForm.get('email')?.hasError('required') && registrationForm.get('email')?.touched) {
            <div class="text-error text-sm mt-1">Email is required.</div>
          }
          @if (registrationForm.get('email')?.hasError('email') && registrationForm.get('email')?.touched) {
            <div class="text-error text-sm mt-1">Please enter a valid email.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Phone Number*</span>
          </legend>
          <input class="input input-bordered" formControlName="phoneNumber" placeholder="Phone Number" type="tel" />
          @if (registrationForm.get('phoneNumber')?.invalid && registrationForm.get('phoneNumber')?.touched) {
            <div class="text-error text-sm mt-1">Phone Number is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Date of Birth*</span>
          </legend>
          <input class="input input-bordered" formControlName="dateOfBirth" placeholder="mm/dd/yyyy" type="text" />
          @if (registrationForm.get('dateOfBirth')?.invalid && registrationForm.get('dateOfBirth')?.touched) {
            <div class="text-error text-sm mt-1">Date of Birth is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Gender*</span>
          </legend>
          <select class="select select-bordered" formControlName="gender">
            <option disabled value="">Select Gender</option>
            <option>Male</option>
            <option>Female</option>
            <option>Other</option>
          </select>
          @if (registrationForm.get('gender')?.invalid && registrationForm.get('gender')?.touched) {
            <div class="text-error text-sm mt-1">Gender is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Institute*</span>
          </legend>
          <select class="select select-bordered" formControlName="institute">
            <option disabled value="">Select Institute</option>
          </select>
          @if (registrationForm.get('institute')?.invalid && registrationForm.get('institute')?.touched) {
            <div class="text-error text-sm mt-1">Institute is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Course*</span>
          </legend>
          <select class="select select-bordered" formControlName="course">
            <option disabled value="">Select Course</option>
          </select>
          @if (registrationForm.get('course')?.invalid && registrationForm.get('course')?.touched) {
            <div class="text-error text-sm mt-1">Course is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Address*</span>
          </legend>
          <textarea class="textarea textarea-bordered" formControlName="address" placeholder="Address"></textarea>
          @if (registrationForm.get('address')?.invalid && registrationForm.get('address')?.touched) {
            <div class="text-error text-sm mt-1">Address is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Year of Study*</span>
          </legend>
          <select class="select select-bordered" formControlName="yearOfStudy">
            <option disabled value="">Select Year</option>
          </select>
          @if (registrationForm.get('yearOfStudy')?.invalid && registrationForm.get('yearOfStudy')?.touched) {
            <div class="text-error text-sm mt-1">Year of Study is required.</div>
          }
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Profile Photo</span>
          </legend>
          <input class="file-input file-input-bordered" formControlName="profilePhoto" type="file" />
        </fieldset>
      </div>
      <div class="card-actions justify-end mt-6">
        <button class="btn" type="button">Cancel</button>
        <button class="btn btn-primary" type="submit">Register Student</button>
      </div>
    </form>
  </div>
</div>
