import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

@Component({
  selector: 'app-student-profile',
  imports: [ReactiveFormsModule, CommonModule],
  templateUrl: './student-profile.html',
  styleUrl: './student-profile.css',
  standalone: true,
})
export class StudentProfile {
  registrationForm = new FormGroup({
    studentId: new FormControl('', [Validators.required]),
    firstName: new FormControl('', [Validators.required]),
    lastName: new FormControl('', [Validators.required]),
    email: new FormControl('', [Validators.required, Validators.email]),
    phoneNumber: new FormControl('', [Validators.required]),
    dateOfBirth: new FormControl('', [Validators.required]),
    address: new FormControl('', [Validators.required]),
    gender: new FormControl('', [Validators.required]),
    institute: new FormControl('', [Validators.required]),
    course: new FormControl('', [Validators.required]),
    yearOfStudy: new FormControl('', [Validators.required]),
    profilePhoto: new FormControl(''),
  });

  registerStudent() {
    if (this.registrationForm.valid) {
      console.log(this.registrationForm.value);
    } else {
      console.log('Form is invalid');
    }
  }
}
