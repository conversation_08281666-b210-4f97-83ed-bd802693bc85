import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { Header } from '../header/header';
import { Footer } from '../footer/footer';
import { Sidenav } from '../sidenav/sidenav';
import { Toast } from '../../../atoms/toast/toast';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [RouterModule, Header, Footer, Sidenav, Toast],
  templateUrl: './main-layout.component.html',
  styleUrls: ['./main-layout.component.scss'],
})
export class MainLayoutComponent {}
