<div class="card rounded-none border-t-0 border-b-0">
  <div class="mt-5">
    <ul class="menu text-base-content w-full sidenav-menu">
      <li>
        <button routerLink="/dashboard" routerLinkActive="menu-active">
          <span class="icon-[solar--home-outline]"></span> Dashboard
        </button>
      </li>
      <li class="menu-title mt-4">REGISTRATION</li>
      <li>
        <button routerLink="/registrations/institute" routerLinkActive="menu-active">
          <span class="icon-[material-symbols-light--school-outline]"></span> Institution Registration
        </button>
      </li>
      <li>
        <button routerLink="/registrations/student" routerLinkActive="menu-active">
          <span class="icon-[ph--student-thin]"></span> Student Registration
        </button>
      </li>
      <li>
        <button routerLink="/registrations/trainer" routerLinkActive="menu-active">
          <span class="icon-[ph--student-thin]"></span> Trainer Registration
        </button>
      </li>
      <li>
        <button routerLink="/registrations/parent" routerLinkActive="menu-active">
          <span class="icon-[ph--student-thin]"></span> Parent Registration
        </button>
      </li>
      <li>
        <button routerLink="/registrations/employee" routerLinkActive="menu-active">
          <span class="icon-[proicons--person]"></span> Employee Registration
        </button>
      </li>
      <li class="menu-title mt-4">MANAGEMENT</li>
      <li>
        <button routerLink="/management/attendance" routerLinkActive="menu-active">
          <span class="icon-[garden--notes-stroke-12]"></span>Attendance
        </button>
      </li>
      <li>
        <button routerLink="/management/monitor-student-data" routerLinkActive="menu-active">
          <span class="icon-[solar--notes-line-duotone]"></span>Monitor Student Data
        </button>
      </li>
      <li>
        <button routerLink="/management/track-recordings" routerLinkActive="menu-active">
          <span class="icon-[uil--video]"></span> Track Recordings
        </button>
      </li>
      <li>
        <button routerLink="/management/gallery" routerLinkActive="menu-active">
          <span class="icon-[solar--gallery-outline]"></span> Gallery
        </button>
      </li>
      <li>
        <button routerLink="/management/employee" routerLinkActive="menu-active">
          <span class="icon-[material-symbols--person-book-outline-sharp]"></span> Employee Management
        </button>
      </li>
      <li class="menu-title mt-4">SYSTEM</li>
      <li>
        <button routerLink="/system/job-posting" routerLinkActive="menu-active">
          <span class="icon-[hugeicons--briefcase-04]"></span> Job Posting
        </button>
      </li>
      <li>
        <button routerLink="/system/activity-log" routerLinkActive="menu-active">
          <span class="icon-[icon-park-outline--log]"></span> Activity Log
        </button>
      </li>
      <li>
        <button routerLink="/system/settings" routerLinkActive="menu-active">
          <span class="icon-[iconamoon--settings-thin]"></span> Settings
        </button>
      </li>
    </ul>
  </div>
</div>
