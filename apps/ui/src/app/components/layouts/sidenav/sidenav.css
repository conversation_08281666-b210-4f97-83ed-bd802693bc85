:host {
  display: block;
  width: 15rem; /* 240px */
  flex-shrink: 0;
  background-color: var(--b1);
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);

  /* Sticky positioning */
  position: sticky;
  top: 4rem; /* Height of the header */
  height: calc(100vh - 4rem); /* Fill the remaining viewport height */
  overflow-y: auto; /* Allow internal scrolling */
}

.sidenav-menu {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
}
