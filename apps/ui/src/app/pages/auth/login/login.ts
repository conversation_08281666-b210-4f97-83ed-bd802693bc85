import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { Auth, signInWithEmailAndPassword } from '@angular/fire/auth';
import { Router } from '@angular/router';

@Component({
  selector: 'app-login',
  imports: [ReactiveFormsModule, CommonModule],
  templateUrl: './login.html',
})
export class Login {
  private auth: Auth = inject(Auth);
  private router: Router = inject(Router);

  loginForm = new FormGroup({
    email: new FormControl('', [Validators.required]),
    password: new FormControl('', [Validators.required]),
  });

  async login() {
    if (this.loginForm.valid) {
      const data: any = this.loginForm.value;
      try {
        const response = await signInWithEmailAndPassword(
          this.auth,
          data.email,
          data.password
        );
        console.log('Successfully Logged:', response);
        await this.router.navigate(['/dashboard']);
      } catch (e) {
        if (e.code === 'auth/invalid-credential') {
          console.error('Invalid email or password.');
        } else {
          console.error('Error signing in: ', e);
        }
      }
    } else {
      console.log('Form is invalid');
    }
  }
}
