<div class="flex justify-center items-center h-screen bg-base-300">
  <div class="card w-96 bg-base-100 shadow-xl">
    <div class="card-body">
      <h2 class="card-title justify-center">Login</h2>
      <form (ngSubmit)="login()" [formGroup]="loginForm">
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Email</span>
          </legend>
          <input class="input input-bordered" formControlName="email" placeholder="email" type="email" />
          @if (loginForm.get('email')?.invalid && loginForm.get('email')?.touched) {
            <div class="text-error text-sm mt-1">Email is required.</div>
          }
        </fieldset>
        <fieldset class="form-control mt-4">
          <legend class="label">
            <span class="label-text">Password</span>
          </legend>
          <input class="input input-bordered" formControlName="password" placeholder="password" type="password" />
          @if (loginForm.get('password')?.invalid && loginForm.get('password')?.touched) {
            <div class="text-error text-sm mt-1">Password is required.</div>
          }
        </fieldset>
        <div class="flex justify-between items-center mt-4">
          <div class="form-control">
            <label class="label cursor-pointer">
              <input class="checkbox checkbox-primary" type="checkbox" />
              <span class="label-text ml-2">Remember me</span>
            </label>
          </div>
          <div>
            <a class="label-text-alt link link-hover" href="/auth/forgot-password">Forgot password?</a>
          </div>
        </div>
        <div class="form-control mt-6">
          <button class="btn btn-primary btn-block" type="submit">Login</button>
        </div>
      </form>
      <div class="divider">New to Worthy Freshers?</div>
      <a class="btn btn-outline btn-primary" href="/auth/register">Create New Account</a>
    </div>
  </div>
</div>
