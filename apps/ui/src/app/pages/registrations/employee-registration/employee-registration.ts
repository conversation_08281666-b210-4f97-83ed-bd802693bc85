import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  addDoc,
  collection,
  collectionData,
  Firestore,
} from '@angular/fire/firestore';
import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-employee-registration',
  imports: [FormsModule, ReactiveFormsModule, CommonModule],
  templateUrl: './employee-registration.html',
})
export class EmployeeRegistration implements OnInit {
  employees$!: Observable<any[]>;
  registrationForm = new FormGroup({
    employeeId: new FormControl('E91011', [Validators.required]),
    fullName: new FormControl('<PERSON>', [Validators.required]),
    department: new FormControl('Human Resources', [Validators.required]),
    designation: new FormControl('HR Manager', [Validators.required]),
    email: new FormControl('<EMAIL>', [
      Validators.required,
      Validators.email,
    ]),
    phoneNumber: new FormControl('1122334455', [Validators.required]),
    dateOfJoining: new FormControl('06/15/2021', [Validators.required]),
    salary: new FormControl('80000', [Validators.required]),
    institute: new FormControl('Test Institute', [Validators.required]),
  });
  private firestore: Firestore = inject(Firestore);

  ngOnInit() {
    const employeesCollection = collection(this.firestore, 'employees');
    this.employees$ = collectionData(employeesCollection);
    this.employees$.subscribe((data) => {
      console.log('Employees:', data);
    });
  }

  async registerEmployee() {
    if (this.registrationForm.valid) {
      const data = this.registrationForm.value;
      const employeesCollection = collection(this.firestore, 'employees');
      try {
        await addDoc(employeesCollection, data);
        console.log('Employee registered successfully!');
        this.registrationForm.reset();
      } catch (error) {
        console.error('Error saving employee data:', error);
      }
    } else {
      console.log('Form is invalid');
    }
  }
}
