<div class="card bg-base-100 shadow-sm shadow-primary">
  <div class="card-body">
    <h2 class="card-title mb-5">Employee Registration Form</h2>
    <form (ngSubmit)="registerEmployee()" [formGroup]="registrationForm">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-5">

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Employee ID*</span></legend>
          <input class="input input-bordered" formControlName="employeeId" placeholder="Employee ID" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Full Name*</span></legend>
          <input class="input input-bordered" formControlName="fullName" placeholder="Full Name" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Department*</span></legend>
          <select class="select select-bordered" formControlName="department">
            <option disabled value="">Select Department</option>
          </select>
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Designation*</span></legend>
          <input class="input input-bordered" formControlName="designation" placeholder="Designation" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Email Address*</span></legend>
          <input class="input input-bordered" formControlName="email" placeholder="Email Address" type="email" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Phone Number*</span></legend>
          <input class="input input-bordered" formControlName="phoneNumber" placeholder="Phone Number" type="tel" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Date of Joining*</span></legend>
          <input class="input input-bordered" formControlName="dateOfJoining" placeholder="mm/dd/yyyy" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Salary*</span></legend>
          <input class="input input-bordered" formControlName="salary" placeholder="Salary" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Institute*</span></legend>
          <select class="select select-bordered" formControlName="institute">
            <option disabled value="">Select Institute</option>
          </select>
        </fieldset>
      </div>

      <div class="divider"></div>
      <div class="card-actions justify-end mt-6 flex gap-5">
        <button class="btn" type="button">Cancel</button>
        <button class="btn btn-primary" type="submit">Register Employee</button>
      </div>
    </form>
  </div>
</div>

<div class="card bg-base-100 shadow-sm shadow-primary mt-6">
  <div class="card-body">
    <h2 class="card-title mb-5">Registered Employees</h2>
    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
        <tr>
          <th>Employee ID</th>
          <th>Name</th>
          <th>Email</th>
          <th>Department</th>
          <th>Designation</th>
        </tr>
        </thead>
        <tbody>
          @if ((employees$ | async)?.length === 0) {
            <tr>
              <td colspan="5" class="text-center">No employees found.</td>
            </tr>
          }
          @for (emp of employees$ | async; track emp) {
            <tr>
              <td>{{ emp.employeeId }}</td>
              <td>{{ emp.fullName }}</td>
              <td>{{ emp.email }}</td>
              <td>{{ emp.department }}</td>
              <td>{{ emp.designation }}</td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
