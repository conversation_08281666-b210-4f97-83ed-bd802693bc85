import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  addDoc,
  collection,
  collectionData,
  Firestore,
} from '@angular/fire/firestore';
import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-parent-registration',
  imports: [FormsModule, ReactiveFormsModule, CommonModule],
  templateUrl: './parent-registration.html',
})
export class ParentRegistration implements OnInit {
  parents$!: Observable<any[]>;
  registrationForm = new FormGroup({
    parentName: new FormControl('<PERSON>', [Validators.required]),
    relationship: new FormControl('Mother', [Validators.required]),
    studentName: new FormControl('John Doe', [Validators.required]),
    email: new FormControl('<EMAIL>', [
      Validators.required,
      Validators.email,
    ]),
    phoneNumber: new FormControl('5556667777', [Validators.required]),
    occupation: new FormControl('Homemaker'),
    address: new FormControl('123 Test Street, Test City', [
      Validators.required,
    ]),
  });
  private firestore: Firestore = inject(Firestore);

  ngOnInit() {
    const parentsCollection = collection(this.firestore, 'parents');
    this.parents$ = collectionData(parentsCollection);
    this.parents$.subscribe((data) => {
      console.log('Parents:', data);
    });
  }

  async registerParent() {
    if (this.registrationForm.valid) {
      const data = this.registrationForm.value;
      const parentsCollection = collection(this.firestore, 'parents');
      try {
        await addDoc(parentsCollection, data);
        console.log('Parent registered successfully!');
        this.registrationForm.reset();
      } catch (error) {
        console.error('Error saving parent data:', error);
      }
    } else {
      console.log('Form is invalid');
    }
  }
}
