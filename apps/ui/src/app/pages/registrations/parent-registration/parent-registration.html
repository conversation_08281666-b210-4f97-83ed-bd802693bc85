<div class="card bg-base-100 shadow-sm shadow-primary">
  <div class="card-body">
    <h2 class="card-title mb-5">Parent Registration Form</h2>
    <form (ngSubmit)="registerParent()" [formGroup]="registrationForm">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-5">

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Parent/Guardian Name*</span></legend>
          <input class="input input-bordered" formControlName="parentName" placeholder="Parent/Guardian Name"
                 type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Relationship*</span></legend>
          <input class="input input-bordered" formControlName="relationship" placeholder="e.g., Mother" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Student Name*</span></legend>
          <select class="select select-bordered" formControlName="studentName">
            <option disabled value="">Select Student</option>
          </select>
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Email Address*</span></legend>
          <input class="input input-bordered" formControlName="email" placeholder="Email Address" type="email" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Phone Number*</span></legend>
          <input class="input input-bordered" formControlName="phoneNumber" placeholder="Phone Number" type="tel" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Occupation</span></legend>
          <input class="input input-bordered" formControlName="occupation" placeholder="Occupation" type="text" />
        </fieldset>

        <fieldset class="form-control md:col-span-3">
          <legend class="label"><span class="label-text">Address*</span></legend>
          <textarea class="textarea textarea-bordered" formControlName="address" placeholder="Address"></textarea>
        </fieldset>
      </div>

      <div class="divider"></div>
      <div class="card-actions justify-end mt-6 flex gap-5">
        <button class="btn" type="button">Cancel</button>
        <button class="btn btn-primary" type="submit">Register Parent</button>
      </div>
    </form>
  </div>
</div>

<div class="card bg-base-100 shadow-sm shadow-primary mt-6">
  <div class="card-body">
    <h2 class="card-title mb-5">Registered Parents</h2>
    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
        <tr>
          <th>Parent Name</th>
          <th>Student Name</th>
          <th>Email</th>
          <th>Phone Number</th>
        </tr>
        </thead>
        <tbody>
          @if ((parents$ | async)?.length === 0) {
            <tr>
              <td colspan="4" class="text-center">No parents found.</td>
            </tr>
          }
          @for (parent of parents$ | async; track parent) {
            <tr>
              <td>{{ parent.parentName }}</td>
              <td>{{ parent.studentName }}</td>
              <td>{{ parent.email }}</td>
              <td>{{ parent.phoneNumber }}</td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
