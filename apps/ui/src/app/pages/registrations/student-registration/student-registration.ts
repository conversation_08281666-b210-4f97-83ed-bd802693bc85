import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  addDoc,
  collection,
  collectionData,
  Firestore,
} from '@angular/fire/firestore';
import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-student-registration',
  imports: [FormsModule, ReactiveFormsModule, CommonModule],
  templateUrl: './student-registration.html',
})
export class StudentRegistration implements OnInit {
  students$!: Observable<any[]>;
  registrationForm = new FormGroup({
    studentId: new FormControl('S12345', [Validators.required]),
    firstName: new FormControl('John', [Validators.required]),
    lastName: new FormControl('Doe', [Validators.required]),
    email: new FormControl('<EMAIL>', [
      Validators.required,
      Validators.email,
    ]),
    phoneNumber: new FormControl('1234567890', [Validators.required]),
    dateOfBirth: new FormControl('01/01/2000', [Validators.required]),
    address: new FormControl('123 Test Street, Test City', [
      Validators.required,
    ]),
    gender: new FormControl('Male', [Validators.required]),
    institute: new FormControl('Test Institute', [Validators.required]),
    course: new FormControl('Computer Science', [Validators.required]),
    yearOfStudy: new FormControl('3', [Validators.required]),
    profilePhoto: new FormControl(''),
  });
  private firestore: Firestore = inject(Firestore);

  ngOnInit() {
    const studentsCollection = collection(this.firestore, 'students');
    this.students$ = collectionData(studentsCollection);
    this.students$.subscribe((data) => {
      console.log('Students:', data);
    });
  }

  async registerStudent() {
    if (this.registrationForm.valid) {
      const data = this.registrationForm.value;
      const studentsCollection = collection(this.firestore, 'students');
      try {
        await addDoc(studentsCollection, data);
        console.log('Student registered successfully!');
        this.registrationForm.reset();
      } catch (error) {
        console.error('Error saving student data:', error);
      }
    } else {
      console.log('Form is invalid');
    }
  }
}
