<div class="card bg-base-100 shadow-sm shadow-primary">
  <div class="card-body">
    <h2 class="card-title mb-5">Student Registration Form</h2>
    <form (ngSubmit)="registerStudent()" [formGroup]="registrationForm">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-5">
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Student ID*</span>
          </legend>
          <input class="input input-bordered" formControlName="studentId" placeholder="Student ID" type="text" />
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">First Name*</span>
          </legend>
          <input class="input input-bordered" formControlName="firstName" placeholder="First Name" type="text" />
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Last Name*</span>
          </legend>
          <input class="input input-bordered" formControlName="lastName" placeholder="Last Name" type="text" />
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Email Address*</span>
          </legend>
          <input class="input input-bordered" formControlName="email" placeholder="Email Address" type="email" />
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Phone Number*</span>
          </legend>
          <input class="input input-bordered" formControlName="phoneNumber" placeholder="Phone Number" type="tel" />
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Date of Birth*</span>
          </legend>
          <input class="input input-bordered" formControlName="dateOfBirth" placeholder="mm/dd/yyyy" type="text" />
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Gender*</span>
          </legend>
          <select class="select select-bordered" formControlName="gender">
            <option disabled value="">Select Gender</option>
            <option>Male</option>
            <option>Female</option>
            <option>Other</option>
          </select>
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Institute*</span>
          </legend>
          <select class="select select-bordered" formControlName="institute">
            <option disabled value="">Select Institute</option>
          </select>
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Course*</span>
          </legend>
          <select class="select select-bordered" formControlName="course">
            <option disabled value="">Select Course</option>
          </select>
        </fieldset>
        <fieldset class="form-control md:col-span-3">
          <legend class="label">
            <span class="label-text">Address*</span>
          </legend>
          <textarea class="textarea textarea-bordered" formControlName="address" placeholder="Address"></textarea>
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Year of Study*</span>
          </legend>
          <select class="select select-bordered" formControlName="yearOfStudy">
            <option disabled value="">Select Year</option>
          </select>
        </fieldset>
        <fieldset class="form-control">
          <legend class="label">
            <span class="label-text">Profile Photo</span>
          </legend>
          <input class="file-input file-input-bordered" formControlName="profilePhoto" type="file" />
        </fieldset>
      </div>
      <div class="divider"></div>
      <div class="card-actions justify-end mt-6 flex gap-5">
        <button class="btn" type="button">Cancel</button>
        <button class="btn btn-primary" type="submit">Register Student</button>
      </div>
    </form>
  </div>
</div>

<div class="card bg-base-100 shadow-sm shadow-primary mt-6">
  <div class="card-body">
    <h2 class="card-title mb-5">Registered Students</h2>
    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
        <tr>
          <th>Student ID</th>
          <th>Name</th>
          <th>Email</th>
          <th>Course</th>
          <th>Institute</th>
        </tr>
        </thead>
        <tbody>
          @if ((students$ | async)?.length === 0) {
            <tr>
              <td colspan="5" class="text-center">No students found.</td>
            </tr>
          }
          @for (student of students$ | async; track student) {
            <tr>
              <td>{{ student.studentId }}</td>
              <td>{{ student.firstName }} {{ student.lastName }}</td>
              <td>{{ student.email }}</td>
              <td>{{ student.course }}</td>
              <td>{{ student.institute }}</td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
