import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  addDoc,
  collection,
  collectionData,
  Firestore,
} from '@angular/fire/firestore';
import { Observable } from 'rxjs';
import { AsyncPipe } from '@angular/common';

@Component({
  selector: 'app-institute-registration',
  imports: [FormsModule, ReactiveFormsModule, AsyncPipe],
  templateUrl: './institute-registration.html',
})
export class InstituteRegistration implements OnInit {
  institutions$!: Observable<any[]>;
  registrationForm = new FormGroup({
    collegeName: new FormControl('Test College', [Validators.required]),
    instituteCode: new FormControl('TC123', [Validators.required]),
    instituteType: new FormControl('Engineering', [Validators.required]),
    university: new FormControl('Test University', [Validators.required]),
    accreditation: new FormControl('NAAC A+'),
    email: new FormControl('<EMAIL>', [
      Validators.required,
      Validators.email,
    ]),
    contactNumber: new FormControl('1234567890', [Validators.required]),
    websiteUrl: new FormControl('www.testcollege.com'),
    principalName: new FormControl('Dr. Test Principal', [Validators.required]),
    principalEmail: new FormControl('<EMAIL>', [
      Validators.required,
      Validators.email,
    ]),
    principalPhone: new FormControl('0987654321', [Validators.required]),
    coordinatorName: new FormControl('Mr. Test Coordinator'),
    coordinatorEmail: new FormControl('<EMAIL>', [
      Validators.email,
    ]),
    coordinatorPhone: new FormControl('1122334455'),
    username: new FormControl('testuser', [Validators.required]),
    instituteLogo: new FormControl(''),
    accreditationDocument: new FormControl(''),
  });
  private firestore: Firestore = inject(Firestore);

  ngOnInit() {
    const institutionsCollection = collection(this.firestore, 'institutions');
    this.institutions$ = collectionData(institutionsCollection);
  }

  async registerInstitute() {
    if (this.registrationForm.valid) {
      const data = this.registrationForm.value;
      const institutionsCollection = collection(this.firestore, 'institutions');
      try {
        await addDoc(institutionsCollection, data);
        console.log('Institute registered successfully!');
        this.registrationForm.reset();
      } catch (error) {
        console.error('Error saving institute data:', error);
      }
    } else {
      console.log('Form is invalid');
    }
  }
}
