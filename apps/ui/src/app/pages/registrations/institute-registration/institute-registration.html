<div class="card bg-base-100 shadow-sm shadow-primary">
  <div class="card-body">
    <h2 class="card-title mb-5">College / Institute Registration Form</h2>
    <form (ngSubmit)="registerInstitute()" [formGroup]="registrationForm">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-5">

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">College Name*</span></legend>
          <input class="input input-bordered" formControlName="collegeName" placeholder="College Name" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Institute Code*</span></legend>
          <input class="input input-bordered" formControlName="instituteCode" placeholder="Institute Code"
                 type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Institute Type*</span></legend>
          <select class="select select-bordered" formControlName="instituteType">
            <option disabled value="">Select Institute Type</option>
            <option value="Government">Government</option>
            <option value="Private">Private</option>
            <option value="Autonomus">Autonomous</option>
          </select>
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">University*</span></legend>
          <input class="input input-bordered" formControlName="university" placeholder="University" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Accreditation</span></legend>
          <input class="input input-bordered" formControlName="accreditation" placeholder="Accreditation" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Email Address*</span></legend>
          <input class="input input-bordered" formControlName="email" placeholder="Email Address" type="email" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Contact Number*</span></legend>
          <input class="input input-bordered" formControlName="contactNumber" placeholder="Contact Number" type="tel" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Website URL</span></legend>
          <input class="input input-bordered" formControlName="websiteUrl" placeholder="Website URL" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Principal Name*</span></legend>
          <input class="input input-bordered" formControlName="principalName" placeholder="Principal Name"
                 type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Principal Email*</span></legend>
          <input class="input input-bordered" formControlName="principalEmail" placeholder="Principal Email"
                 type="email" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Principal Phone*</span></legend>
          <input class="input input-bordered" formControlName="principalPhone" placeholder="Principal Phone"
                 type="tel" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Coordinator Name</span></legend>
          <input class="input input-bordered" formControlName="coordinatorName" placeholder="Coordinator Name"
                 type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Coordinator Email</span></legend>
          <input class="input input-bordered" formControlName="coordinatorEmail" placeholder="Coordinator Email"
                 type="email" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Coordinator Phone</span></legend>
          <input class="input input-bordered" formControlName="coordinatorPhone" placeholder="Coordinator Phone"
                 type="tel" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Username*</span></legend>
          <input class="input input-bordered" formControlName="username" placeholder="Username" type="text" />
        </fieldset>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-5 mt-4">
        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Institute Logo</span></legend>
          <input class="file-input file-input-bordered" formControlName="instituteLogo" type="file" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Accreditation Document</span></legend>
          <input class="file-input file-input-bordered" formControlName="accreditationDocument" type="file" />
        </fieldset>
      </div>

      <div class="divider"></div>
      <div class="card-actions justify-end mt-6 flex gap-5">
        <button class="btn" type="button">Cancel</button>
        <button class="btn btn-primary" type="submit">Register Institute</button>
      </div>
    </form>
  </div>
</div>

<div class="card bg-base-100 shadow-sm shadow-primary mt-6">
  <div class="card-body">
    <h2 class="card-title mb-5">Registered Institutions</h2>
    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
        <tr>
          <th>College Name</th>
          <th>Institute Code</th>
          <th>Email</th>
          <th>Principal</th>
          <th>Contact</th>
        </tr>
        </thead>
        <tbody>
          @if ((institutions$ | async)?.length === 0) {
            <tr>
              <td colspan="5" class="text-center">No institutions found.</td>
            </tr>
          }
          @for (inst of (institutions$ | async); track inst) {
            <tr>
              <td>{{ inst.collegeName }}</td>
              <td>{{ inst.instituteCode }}</td>
              <td>{{ inst.email }}</td>
              <td>{{ inst.principalName }}</td>
              <td>{{ inst.contactNumber }}</td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
