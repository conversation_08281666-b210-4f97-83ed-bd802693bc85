<div class="card bg-base-100 shadow-sm shadow-primary">
  <div class="card-body">
    <h2 class="card-title mb-5">Trainer Registration Form</h2>
    <form (ngSubmit)="registerTrainer()" [formGroup]="registrationForm">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-5">

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Trainer ID*</span></legend>
          <input class="input input-bordered" formControlName="trainerId" placeholder="Trainer ID" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Full Name*</span></legend>
          <input class="input input-bordered" formControlName="fullName" placeholder="Full Name" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Email Address*</span></legend>
          <input class="input input-bordered" formControlName="email" placeholder="Email Address" type="email" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Phone Number*</span></legend>
          <input class="input input-bordered" formControlName="phoneNumber" placeholder="Phone Number" type="tel" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Specialization*</span></legend>
          <input class="input input-bordered" formControlName="specialization" placeholder="Specialization"
                 type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Experience (Years)*</span></legend>
          <input class="input input-bordered" formControlName="experience" placeholder="Experience (Years)"
                 type="number" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Qualification*</span></legend>
          <input class="input input-bordered" formControlName="qualification" placeholder="Qualification" type="text" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Institute*</span></legend>
          <select class="select select-bordered" formControlName="institute">
            <option disabled value="">Select Institute</option>
          </select>
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Employment Type*</span></legend>
          <select class="select select-bordered" formControlName="employmentType">
            <option disabled value="">Select Employment Type</option>
          </select>
        </fieldset>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-5 mt-4">
        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Resume/CV</span></legend>
          <input class="file-input file-input-bordered" formControlName="resume" type="file" />
        </fieldset>

        <fieldset class="form-control">
          <legend class="label"><span class="label-text">Profile Photo</span></legend>
          <input class="file-input file-input-bordered" formControlName="profilePhoto" type="file" />
        </fieldset>
      </div>

      <div class="divider"></div>
      <div class="card-actions justify-end mt-6 flex gap-5">
        <button class="btn" type="button">Cancel</button>
        <button class="btn btn-primary" type="submit">Register Trainer</button>
      </div>
    </form>
  </div>
</div>

<div class="card bg-base-100 shadow-sm shadow-primary mt-6">
  <div class="card-body">
    <h2 class="card-title mb-5">Registered Trainers</h2>
    <div class="overflow-x-auto">
      <table class="table table-zebra w-full">
        <thead>
        <tr>
          <th>Trainer ID</th>
          <th>Name</th>
          <th>Email</th>
          <th>Specialization</th>
          <th>Institute</th>
        </tr>
        </thead>
        <tbody>
          @if ((trainers$ | async)?.length === 0) {
            <tr>
              <td colspan="5" class="text-center">No trainers found.</td>
            </tr>
          }
          @for (trainer of trainers$ | async; track trainer) {
            <tr>
              <td>{{ trainer.trainerId }}</td>
              <td>{{ trainer.fullName }}</td>
              <td>{{ trainer.email }}</td>
              <td>{{ trainer.specialization }}</td>
              <td>{{ trainer.institute }}</td>
            </tr>
          }
        </tbody>
      </table>
    </div>
  </div>
</div>
