import { Component, inject, OnInit } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  addDoc,
  collection,
  collectionData,
  Firestore,
} from '@angular/fire/firestore';
import { Observable } from 'rxjs';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-trainer-registration',
  imports: [FormsModule, ReactiveFormsModule, CommonModule],
  templateUrl: './trainer-registration.html',
})
export class TrainerRegistration implements OnInit {
  trainers$!: Observable<any[]>;
  registrationForm = new FormGroup({
    trainerId: new FormControl('T5678', [Validators.required]),
    fullName: new FormControl('<PERSON>', [Validators.required]),
    email: new FormControl('<EMAIL>', [
      Validators.required,
      Validators.email,
    ]),
    phoneNumber: new FormControl('0987654321', [Validators.required]),
    specialization: new FormControl('Web Development', [Validators.required]),
    experience: new FormControl('5', [Validators.required]),
    qualification: new FormControl('M.Sc. Computer Science', [
      Validators.required,
    ]),
    institute: new FormControl('Test Institute', [Validators.required]),
    employmentType: new FormControl('Full-time', [Validators.required]),
    resume: new FormControl(''),
    profilePhoto: new FormControl(''),
  });
  private firestore: Firestore = inject(Firestore);

  ngOnInit() {
    const trainersCollection = collection(this.firestore, 'trainers');
    this.trainers$ = collectionData(trainersCollection);
    this.trainers$.subscribe((data) => {
      console.log('Trainers:', data);
    });
  }

  async registerTrainer() {
    if (this.registrationForm.valid) {
      const data = this.registrationForm.value;
      const trainersCollection = collection(this.firestore, 'trainers');
      try {
        await addDoc(trainersCollection, data);
        console.log('Trainer registered successfully!');
        this.registrationForm.reset();
      } catch (error) {
        console.error('Error saving trainer data:', error);
      }
    } else {
      console.log('Form is invalid');
    }
  }
}
