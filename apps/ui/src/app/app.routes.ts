import { Route } from '@angular/router';
import { MainLayoutComponent } from './components/layouts/main-layout/main-layout.component';
import { LandingLayoutComponent } from './components/layouts/landing-layout/landing-layout.component';
import { AuthLayoutComponent } from './components/layouts/auth-layout/auth-layout.component';
import {
  canActivate,
  hasCustomClaim,
  redirectLoggedInTo,
  redirectUnauthorizedTo,
} from '@angular/fire/auth-guard';

const adminOnly = () => hasCustomClaim('admin');
const redirectUnauthorizedToLogin = () =>
  redirectUnauthorizedTo(['auth/login']);
const redirectLoggedInToDashboard = () => redirectLoggedInTo(['/dashboard']);
const belongsToAccount = (next) => hasCustomClaim(`account-${next.params.id}`);

export const appRoutes: Route[] = [
  // 1. Landing Page Layout (no header/footer)
  {
    path: '',
    component: LandingLayoutComponent,
    ...canActivate(redirectLoggedInToDashboard),
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadComponent: () =>
          import('./pages/landing/landing').then((m) => m.Landing),
      },
    ],
  },

  // 2. Auth Layout (header/footer, no sidenav)
  {
    path: 'auth',
    component: AuthLayoutComponent,
    ...canActivate(redirectLoggedInToDashboard),
    children: [
      {
        path: 'login',
        loadComponent: () =>
          import('./pages/auth/login/login').then((m) => m.Login),
      },
      {
        path: 'register',
        loadComponent: () =>
          import('./pages/auth/register/register').then((m) => m.Register),
      },
      {
        path: 'forgot-password',
        loadComponent: () =>
          import('./pages/auth/forgot-password/forgot-password').then(
            (m) => m.ForgotPassword
          ),
      },
      {
        path: 'terms-and-conditions',
        loadComponent: () =>
          import('./pages/auth/terms-and-conditions/terms-and-conditions').then(
            (m) => m.TermsAndConditions
          ),
      },
    ],
  },

  // 3. Main App Layout (header/footer/sidenav)
  {
    path: '',
    component: MainLayoutComponent,
    ...canActivate(redirectUnauthorizedToLogin),
    children: [
      {
        path: 'dashboard',
        loadComponent: () =>
          import('./pages/dashboard/dashboard').then((m) => m.Dashboard),
      },
      {
        path: 'registrations',
        children: [
          {
            path: 'institute',
            loadComponent: () =>
              import(
                './pages/registrations/institute-registration/institute-registration'
              ).then((m) => m.InstituteRegistration),
          },
          {
            path: 'student',
            loadComponent: () =>
              import(
                './pages/registrations/student-registration/student-registration'
              ).then((m) => m.StudentRegistration),
          },
          {
            path: 'trainer',
            loadComponent: () =>
              import(
                './pages/registrations/trainer-registration/trainer-registration'
              ).then((m) => m.TrainerRegistration),
          },
          {
            path: 'parent',
            loadComponent: () =>
              import(
                './pages/registrations/parent-registration/parent-registration'
              ).then((m) => m.ParentRegistration),
          },
          {
            path: 'employee',
            loadComponent: () =>
              import(
                './pages/registrations/employee-registration/employee-registration'
              ).then((m) => m.EmployeeRegistration),
          },
        ],
      },
      {
        path: 'system',
        children: [
          {
            path: 'settings',
            loadComponent: () =>
              import('./pages/settings/settings').then((m) => m.Settings),
          },
        ],
      },
      {
        path: 'home',
        redirectTo: 'dashboard',
        pathMatch: 'full',
      },
    ],
  },
];
