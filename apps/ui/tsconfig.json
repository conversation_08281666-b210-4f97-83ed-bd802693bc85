{"extends": "../../tsconfig.base.json", "compilerOptions": {"strict": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "target": "es2022", "moduleResolution": "bundler", "isolatedModules": true, "emitDecoratorMetadata": false, "module": "preserve"}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "typeCheckHostBindings": true, "strictTemplates": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.app.json"}, {"path": "./tsconfig.spec.json"}]}