{"name": "ui", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "prefix": "app", "sourceRoot": "apps/ui/src", "tags": [], "targets": {"build": {"executor": "@angular/build:application", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/ui", "browser": "apps/ui/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "apps/ui/tsconfig.app.json", "assets": [{"glob": "**/*", "input": "apps/ui/public"}], "styles": ["apps/ui/src/styles.css"]}, "configurations": {"prod": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "1mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "outputHashing": "all"}, "dev": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "apps/ui/src/environments/environment.ts", "with": "apps/ui/src/environments/environment.dev.ts"}]}, "local": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "local"}, "serve": {"continuous": true, "executor": "@angular/build:dev-server", "configurations": {"prod": {"buildTarget": "ui:build:prod"}, "dev": {"buildTarget": "ui:build:dev"}, "local": {"buildTarget": "ui:build:local"}}, "defaultConfiguration": "local"}, "extract-i18n": {"executor": "@angular/build:extract-i18n", "options": {"buildTarget": "ui:build"}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/ui/jest.config.ts", "tsConfig": "apps/ui/tsconfig.spec.json"}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "ui:build", "port": 4200, "staticFilePath": "dist/apps/ui/browser", "spa": true}}}}