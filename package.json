{"name": "worthy-freshers", "version": "0.0.0", "license": "MIT", "scripts": {"build:dev": "ng build --configuration=dev", "build:qa": "ng build --configuration=qa", "build:prod": "ng build --configuration=prod", "build:functions": "nx run firebase:build", "deploy:dev": "npm run build:dev && firebase deploy --only hosting:dev", "deploy:qa": "npm run build:qa && firebase deploy --only hosting:qa", "deploy:prod": "npm run build:prod && firebase deploy --only hosting:prod", "deploy:functions": "firebase deploy --only functions", "build:deploy:functions": "npm run build:functions && npm run deploy:functions"}, "private": true, "dependencies": {"@angular/common": "~20.2.0", "@angular/compiler": "~20.2.0", "@angular/core": "~20.2.0", "@angular/fire": "^20.0.1", "@angular/forms": "~20.2.0", "@angular/platform-browser": "~20.2.0", "@angular/platform-browser-dynamic": "~20.2.0", "@angular/router": "~20.2.0", "rxjs": "~7.8.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/core": "~20.2.0", "@angular-devkit/schematics": "~20.2.0", "@angular/build": "~20.2.0", "@angular/cli": "~20.2.0", "@angular/compiler-cli": "~20.2.0", "@angular/language-service": "~20.2.0", "@eslint/js": "^9.8.0", "@iconify/json": "^2.2.384", "@iconify/tailwind4": "^1.0.6", "@nx/angular": "21.5.2", "@nx/devkit": "21.5.2", "@nx/esbuild": "21.5.2", "@nx/eslint": "21.5.2", "@nx/eslint-plugin": "21.5.2", "@nx/jest": "21.5.2", "@nx/js": "21.5.2", "@nx/playwright": "21.5.2", "@nx/web": "21.5.2", "@nx/workspace": "21.5.2", "@playwright/test": "^1.36.0", "@schematics/angular": "~20.2.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@tailwindcss/postcss": "^4.1.13", "@types/jest": "^30.0.0", "@types/node": "20.19.9", "@typescript-eslint/utils": "^8.40.0", "angular-eslint": "^20.2.0", "daisyui": "^5.1.12", "esbuild": "^0.19.2", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-playwright": "^1.6.2", "firebase-tools": "^14.17.0", "jest": "^30.0.2", "jest-environment-jsdom": "^30.0.2", "jest-environment-node": "^30.0.2", "jest-preset-angular": "~15.0.0", "jest-util": "^30.0.2", "jsonc-eslint-parser": "^2.1.0", "nx": "21.5.2", "postcss": "^8.5.6", "prettier": "^2.6.2", "tailwindcss": "^4.1.13", "ts-jest": "^29.4.0", "ts-node": "10.9.1", "tslib": "^2.3.0", "typescript": "~5.9.2", "typescript-eslint": "^8.40.0"}}