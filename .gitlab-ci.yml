image: node:20
variables:
  CI: 'true'

# Main job
CI:
  interruptible: true
  only:
    - main
    - merge_requests
  script:
    - npm install --prefix=$HOME/.local -g pnpm@8

    # This enables task distribution via Nx Cloud
    # Run this command as early as possible, before dependencies are installed
    # Learn more at https://nx.dev/ci/reference/nx-cloud-cli#npx-nxcloud-startcirun
    # Uncomment this line to enable task distribution
    # - pnpm dlx nx start-ci-run --distribute-on="3 linux-medium-js" --stop-agents-after="e2e-ci"

    - pnpm install --frozen-lockfile
    - pnpm exec playwright install --with-deps

    # Prepend any command with "nx-cloud record --" to record its logs to Nx Cloud
    # - pnpm exec nx-cloud record -- echo Hello World
    # When you enable task distribution, run the e2e-ci task instead of e2e
    - pnpm exec nx run-many -t lint test build e2e
    # Nx Cloud recommends fixes for failures to help you get CI green faster. Learn more: https://nx.dev/ci/features/self-healing-ci

  after_script:
    - pnpm exec nx fix-ci
