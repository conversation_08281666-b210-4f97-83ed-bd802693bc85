import { jwtDecoder } from './jwt-decoder';

const token =
  'eyJhbGciOiJSUzI1NiIsImtpZCI6IjUwMDZlMjc5MTVhMTcwYWIyNmIxZWUzYjgxZDExNjU0MmYxMjRmMjAiLCJ0eXAiOiJKV1QifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bwtG-kEMlUClcomTThmqY6Hi752Q-P4HsObg99_AP1suMtgjLAfJZZURv-SO3QH6DpZkrWu-pAbNje9E3bR1W2PqCy8OnxyeYNKspAwGLKMhzGAaPDrKaSIZqjzAe9CD-hk-Qt9O0bxd0eZ6ASjLpALNfNfeszfFVK6cZSwl0X6KezgAqIHRRnyLIx-SuJGVFOgO3cZ8wWIZtYCOF4Wssqo8koJ9P4w3L5wjqsjos6qbzWg1hohJ1JNXvExrFqoGDP-kxmpgMgb-6reZ_HVPV0obWMuZN2DBhhsFriNx-RGgvJ_-Q41XPP3bqPcxba-45YvCNZwTdlK8OU7HfFWATg';

describe('JWT Decoder', () => {
  it('should decode JWT', () => {
    const jwtData = jwtDecoder(token);
    expect(jwtData).toBeDefined();
    console.log('JWT Data:', JSON.stringify(jwtData));
  });
});

const x = {
  iss: 'https://securetoken.google.com/worthy-freshers',
  aud: 'worthy-freshers',
  auth_time: **********,
  user_id: 'Mpp2BtpOcUg5gjbX11K6Kn9JvJf2',
  sub: 'Mpp2BtpOcUg5gjbX11K6Kn9JvJf2',
  iat: **********,
  exp: **********,
  email: '<EMAIL>',
  email_verified: false,
  firebase: {
    identities: { email: ['<EMAIL>'] },
    sign_in_provider: 'password',
  },
};
