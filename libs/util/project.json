{"name": "util", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/util/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/util", "main": "libs/util/src/index.ts", "tsConfig": "libs/util/tsconfig.lib.json", "assets": ["libs/util/*.md"], "format": ["cjs"], "generatePackageJson": true}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/util/jest.config.ts"}}}}