{"name": "firebase", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "functions/firebase/src", "projectType": "library", "tags": [], "targets": {"build": {"executor": "@nx/esbuild:esbuild", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/functions/firebase", "main": "functions/firebase/src/index.ts", "tsConfig": "functions/firebase/tsconfig.lib.json", "assets": ["functions/firebase/*.md"], "format": ["cjs"], "generatePackageJson": false}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "functions/firebase/jest.config.ts"}}}}