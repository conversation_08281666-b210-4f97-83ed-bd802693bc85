import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

admin.initializeApp();

export const updateUserRole = functions.https.onCall(
  async (request: any, context: any) => {
    const { uid, role } = request.data;
    console.log('Data', request.data);

    if (!uid || !role) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        "The function must be called with two arguments 'uid' and 'role'."
      );
    }

    try {
      // Set custom user claims on the user.
      await admin.auth().setCustomUserClaims(uid, { role });

      return {
        message: `Success! User ${uid} has been given the role of ${role}.`,
      };
    } catch (error) {
      console.error(error);
      throw new functions.https.HttpsError(
        'internal',
        'Unable to update user role.'
      );
    }
  }
);
