{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.mjs", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/src/test-setup.[jt]s", "!{projectRoot}/test-setup.[jt]s"], "sharedGlobals": ["{workspaceRoot}/.gitlab-ci.yml"]}, "nxCloudId": "68c74d435b1cd94f10111eb7", "targetDefaults": {"@angular/build:application": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/eslint:lint": {"cache": true, "inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore", "{workspaceRoot}/eslint.config.mjs"]}, "@nx/jest:jest": {"cache": true, "inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "options": {"passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "@nx/esbuild:esbuild": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "plugins": [{"plugin": "@nx/playwright/plugin", "options": {"targetName": "e2e"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "lint"}}], "generators": {"@nx/angular:application": {"e2eTestRunner": "playwright", "linter": "eslint", "style": "css", "unitTestRunner": "jest"}, "@nx/angular:component": {"style": "none", "skipTests": true}, "@nx/angular:guard": {"skipTests": true}, "@nx/angular:service": {"skipTests": true}, "@nx/angular:pipe": {"skipTests": true}, "@nx/angular:directive": {"skipTests": true}}}